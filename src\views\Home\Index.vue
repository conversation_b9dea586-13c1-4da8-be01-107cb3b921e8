<template>
	<v-main class="custom white" style="height: 100vh;">
		<vue-scroll @handle-scroll="scrollEvent" style="max-height: calc(100vh - 48px);">
			<div style="max-height: calc(100vh - 48px);">
			
				
				<!-- 상단 검색창과 프로필 -->
				<div class="d-flex align-center justify-center px-3 py-2 home-search-container" v-if="isLogin">
					<div class="search-width mr-3">
						<search-box @search="onSearch"></search-box>
					</div>
					<div class="home-profile-container">
						<v-menu
							v-model="avatarMenu"
							:close-on-content-click="false"
							offset-y
							left
							transition="slide-y-transition"
							:nudge-width="250"
							:nudge-bottom="10">
							<template v-slot:activator="{ on, attrs }">
								<v-avatar size="36" class="no-drag" v-bind="attrs" v-on="on">
									<img :src="$store.getters.user.profile_url">
								</v-avatar>
							</template>
							<v-card color="white" elevation="3" class="profile-popup rounded-lg">
								<v-list-item class="px-2">
									<v-list-item-avatar size="56" color="grey lighten-3">
										<v-img :src="$store.getters.user.profile_url"></v-img>
									</v-list-item-avatar>

									<v-list-item-content class="ml-2">
										<v-list-item-title class="title font-weight-bold" style="font-size: 1.1rem !important;">
											{{ $store.getters.user.nickname }}
										</v-list-item-title>
										<v-list-item-subtitle class="mt-1" style="font-size: 0.8rem !important; color: #666;">
											@{{ $store.getters.user.tag }}
										</v-list-item-subtitle>
									</v-list-item-content>
								</v-list-item>
								
								<v-divider class="mx-4 my-2"></v-divider>
								
								<v-list dense class="profile-action-list">
									<v-list-item link @click="$assign(userLink)" class="profile-action-item">
										<v-list-item-icon class="mr-3">
											<v-icon size="20" color="primary">mdi-account</v-icon>
										</v-list-item-icon>
										<v-list-item-content>
											<v-list-item-title>프로필 보기</v-list-item-title>
										</v-list-item-content>
									</v-list-item>
									
									<v-list-item link @click="openFollowingDialog" class="profile-action-item">
										<v-list-item-icon class="mr-3">
											<v-icon size="20" color="purple">mdi-account-multiple</v-icon>
										</v-list-item-icon>
										<v-list-item-content>
											<v-list-item-title>내 팔로잉</v-list-item-title>
										</v-list-item-content>
									</v-list-item>
									
									<v-list-item link @click="spoonLogout" class="profile-action-item">
										<v-list-item-icon class="mr-3">
											<v-icon size="20" color="red darken-1">mdi-logout</v-icon>
										</v-list-item-icon>
										<v-list-item-content>
											<v-list-item-title>{{ $t('spoon-logout') }}</v-list-item-title>
										</v-list-item-content>
									</v-list-item>
								</v-list>
							</v-card>
						</v-menu>
					</div>
				</div>

				<!-- 구독중인 DJ 리스트 -->
				<div v-if="liveSubscribed.length" class="px-3 mt-3">
					<h3 class="mb-2 pl-1">{{ $t('home.following-dj') }}</h3>
					<div class="divider mb-3"></div>
					<div class="list-container">
						<div 
							v-for="(live, idx) in liveSubscribed" 
							:key="'sub' + idx + live.id" 
							class="live-item-wrapper">
							<live-item :live="live" />
						</div>
					</div>
				</div>

				<!-- 멤버십 리스트 -->
				<div v-if="liveMembership.length" class="px-3 mt-3">
					<h3 class="mb-2 pl-1">{{ $t('home.membership') }}</h3>
					<div class="divider mb-3"></div>
					<div class="list-container">
						<div 
							v-for="(live, idx) in liveMembership" 
							:key="'sub' + idx + live.id" 
							class="live-item-wrapper">
							<live-item :live="live" isMembership />
						</div>
					</div>
				</div>

				<!-- 실시간 라이브 리스트 -->
				<div v-if="liveList" class="px-3 mt-3">
					<h3 class="mb-2 pl-3">Live</h3>
					<div class="divider mb-5"></div>
					<div class="list-container">
						<div
							v-for="(live, idx) in liveList"
							:key="'' + idx + live.id"
							class="live-item-wrapper">
							<live-item :live="live" />
						</div>
					</div>

					<!-- 로딩 상태 표시 -->
					<div v-if="asyncMutex && !loadComplete" class="text-center py-4">
						<v-progress-circular indeterminate color="primary" size="24"></v-progress-circular>
						<p class="mt-2 text-body-2 grey--text">더 많은 방송을 불러오는 중...</p>
					</div>

					<!-- 모든 데이터 로드 완료 -->
					<div v-if="loadComplete && liveList.length > 0" class="text-center py-4">
						<p class="text-body-2 grey--text">🎉 모든 방송을 확인했습니다!</p>
					</div>

					<!-- 수동 로드 버튼 (무한스크롤이 작동하지 않을 때 대비) -->
					<div v-if="!loadComplete && !asyncMutex && liveList.length > 0" class="text-center py-4">
						<v-btn
							outlined
							color="primary"
							@click="getNextLiveList"
							class="load-more-btn">
							<v-icon left>mdi-refresh</v-icon>
							더 많은 방송 보기
						</v-btn>
					</div>
				</div>
			</div>
		</vue-scroll>
	</v-main>
</template>
<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import GlobalMixins from '@/plugins/mixins';
import { Live, User } from '@sopia-bot/core';
import LiveItem from './LiveItem.vue';
import SearchBox from '@/views/Search/SearchBox.vue';

const sleep = (msec: number) => {
	return new Promise((resolve, reject) => {
		setTimeout(resolve, msec);
	});
};


@Component({
	components: {
		LiveItem,
		SearchBox,
	},
})
export default class Home extends Mixins(GlobalMixins) {
	// TODO: liveManager is api request struct
	public liveManager!: any;
	public liveList: Live[] = [];
	public liveSubscribed: Live[] = [];
	public liveMembership: Live[] = [];
	public asyncMutex: boolean = false;
	public loadComplete: boolean = false;
	public avatarMenu: boolean = false;

	public get isLogin() {
		return !!this.$store.getters.user;
	}

	public get userLink() {
		return `/user/${this.$store.getters.user.id}`;
	}

	public spoonLogout() {
		window.logout();
	}

	public openFollowingDialog() {
		this.$store.commit('setFollowingDialogOpen', true);
		this.avatarMenu = false;
	}

	// TODO: Can setting audult content
	public async getNextLiveList() {
		console.log('getNextLiveList 호출됨');

		if ( this.loadComplete || this.asyncMutex ) {
			console.log('로드 중단:', { loadComplete: this.loadComplete, asyncMutex: this.asyncMutex });
			return;
		}

		this.asyncMutex = true;
		console.log('라이브 목록 로드 시작...');

		try {
			if ( this.liveManager ) {
				if ( this.liveManager.res.next === '' ) {
					console.log('모든 라이브 목록 로드 완료');
					this.loadComplete = true;
				} else {
					console.log('다음 페이지 로드 중...');
					const res = await this.liveManager.next();
					this.liveManager.res = res;
					const newLives = res.results || [];
					this.liveList = this.liveList.concat(newLives);
					console.log(`${newLives.length}개의 새로운 라이브 추가됨. 총 ${this.liveList.length}개`);
				}
			} else {
				console.log('첫 번째 라이브 목록 로드 중...');
				this.liveManager = await this.$sopia.api.lives.popular();
				this.liveList = this.liveManager.res.results || [];
				console.log(`첫 번째 로드 완료: ${this.liveList.length}개의 라이브`);
			}
		} catch (error) {
			console.error('라이브 목록 로드 실패:', error);
			// 에러 발생 시 사용자에게 알림 (선택사항)
			// this.$store.commit('setSnackbar', { text: '방송 목록을 불러오는데 실패했습니다.', color: 'error' });
		} finally {
			this.asyncMutex = false;
			console.log('라이브 목록 로드 완료');
		}
	}

	public async created() {
		// 파트너 DJ 관련 코드 제거
	}

	public async mounted() {
		this.$evt.$on('user', async (user: User) => {
			this.liveSubscribed = [];

			const req = await this.$sopia.api.lives.subscribed();
			const lives = req.res.results;
			for ( const live of lives ) {
				this.liveSubscribed.push(live);
			}

			const membershipReq = await this.$sopia.api.lives.membership();
			const membershipLives = membershipReq.res.results;
			for ( const live of membershipLives ) {
				this.liveMembership.push(live);
			}
		});
		this.getNextLiveList();
		setTimeout(async () => {
			if ( window.$sopia.logonUser ) {
				this.liveSubscribed = [];

				const req = await this.$sopia.api.lives.subscribed();
				const lives = req.res.results;
				for ( const live of lives ) {
					this.liveSubscribed.push(live);
				}

				const membershipReq = await this.$sopia.api.lives.membership();
				const membershipLives = membershipReq.res.results;
				for ( const live of membershipLives ) {
					this.liveMembership.push(live);
				}
			}
		}, 1000);
	}

	public scrollEvent(vertical: any) {
		console.log('스크롤 이벤트:', vertical.process);

		// 스크롤이 80% 이상일 때 더 빠르게 로드 (기존 90%에서 80%로 변경)
		if ( vertical.process >= 0.8 && !this.loadComplete && !this.asyncMutex ) {
			console.log('무한스크롤 트리거: 다음 라이브 목록 로드 시작');
			this.getNextLiveList();
		}
	}

	public onSearch(query: string) {
		// 검색어가 있으면 검색 페이지로 이동
		if (query.trim()) {
			this.$assign(`/search/user/${encodeURI(query.trim())}`);
		}
	}
}
</script>
<style>
/* 파트너 DJ 관련 스타일 제거 */
.no-drag {
	-webkit-app-region: no-drag;
}
/* home-search-container margin 바꾸지말것*/
.home-search-container {
	background-color: transparent;
	border-radius: 4px;
	margin: 50px 12px 12px 12px;
	display: flex;
	justify-content: center;
}

.home-profile-container {
	margin-left: 12px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.search-width {
	width: 50%;
	max-width: 300px;
}

/* 프로필 팝업 스타일 */
.profile-popup {
	border-radius: 12px;
	overflow: hidden;
	max-width: 280px;
}

.profile-action-list {
	padding: 0 4px;
}

.profile-action-item {
	transition: all 0.2s;
	border-radius: 8px;
	margin: 4px;
	min-height: 42px;
}

.profile-action-item:hover {
	background-color: rgba(0, 0, 0, 0.03);
}

/* 리스트 컨테이너 스타일 */
.list-container {
	margin-bottom: 1rem;
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}

/* 라이브 아이템 래퍼 스타일 */
.live-item-wrapper {
	margin: 8px;
}

/* 구분선 스타일 */
.divider {
	height: 2px;
	background-color: rgba(0, 0, 0, 0.1);
	width: 50px;
}

/* 탭 스타일 */
.tabs-container {
	display: flex;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	margin-bottom: 5px;
}

.tab-btn {
	padding: 8px 16px;
	font-weight: 500;
	color: #888;
	position: relative;
	cursor: pointer;
}

.tab-btn.active {
	color: #f06292;
}

.tab-btn.active::after {
	content: '';
	position: absolute;
	bottom: -1px;
	left: 0;
	width: 100%;
	height: 2px;
	background-color: #f06292;
}

/* 더 많은 방송 보기 버튼 스타일 */
.load-more-btn {
	margin: 16px 0;
	transition: all 0.3s ease;
}

.load-more-btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
